const { saveScheduledMessage, saveMessage } = require("../utils/redisClient");
const { momentNow } = require("../helpers");
const { CONSTANTS } = require("../init");
const moment = require("moment");

/**
 * Organiza e prepara emails para serem salvos no Redis
 * @param {Array} emails - Lista de emails para processar
 * @returns {Promise<void>}
 */
const emailOrganizeMessages = async (emails) => {
  if (!emails || emails.length === 0) {
    console.log(
      "EMAILCRON > EMAILORGANIZE > ORGANIZE MESSAGES > NO EMAILS TO PROCESS"
    );
    return;
  }

  console.log(
    `EMAILCRON > EMAILORGANIZE > ORGANIZE MESSAGES > Processing ${emails.length} emails`
  );

  // Processar os emails em paralelo
  await Promise.all(
    emails.map((email) => {
      return prepareEmailAndSaveInRedis(email);
    })
  );

  console.log(
    `EMAILCRON > EMAILORGANIZE > ORGANIZE MESSAGES > Completed processing ${emails.length} emails`
  );
};

/**
 * Prepara um email individual e salva no Redis
 * @param {Object} email - Dados do email
 * @returns {Promise<void>}
 */
const prepareEmailAndSaveInRedis = async (email) => {
  try {
    console.log(
      "EMAILCRON > EMAILORGANIZE > PREPARE EMAIL",
      "emailId:",
      email.id || "no-id",
      "to:",
      email.to,
      "subject:",
      email.subject
    );

    // Validar dados obrigatórios
    if (!email.to || !email.subject || !email.html) {
      console.error(
        "EMAILCRON > EMAILORGANIZE > PREPARE EMAIL > ERROR: Missing required fields",
        {
          to: !!email.to,
          subject: !!email.subject,
          html: !!email.html,
        }
      );
      return;
    }

    // Determinar o horário de envio
    let scheduledTime;
    if (email.scheduled_date && moment(email.scheduled_date).isValid()) {
      scheduledTime = new Date(email.scheduled_date).getTime();
    } else {
      // Se não há data agendada, enviar imediatamente
      scheduledTime = Date.now();
    }

    // Converter timestamp para data legível para logs
    const scheduledDate = new Date(scheduledTime).toISOString();
    console.log(
      "EMAILCRON > EMAILORGANIZE > PREPARE EMAIL > SCHEDULED TIME",
      "timestamp:",
      scheduledTime,
      "date:",
      scheduledDate
    );

    // Processar destinatários
    if (email.emailVars && typeof email.emailVars === "object") {
      // Email com variáveis por destinatário - criar um email para cada destinatário
      await processEmailWithVariables(email, scheduledTime);
    } else {
      // Email simples - um único email para todos os destinatários
      await processSimpleEmail(email, scheduledTime);
    }
  } catch (error) {
    console.error(
      "EMAILCRON > EMAILORGANIZE > PREPARE EMAIL > ERROR:",
      error.message
    );
    console.error(error.stack);
  }
};

/**
 * Processa email com variáveis por destinatário
 * @param {Object} email - Dados do email
 * @param {number} scheduledTime - Timestamp de agendamento
 * @returns {Promise<void>}
 */
const processEmailWithVariables = async (email, scheduledTime) => {
  const recipients = Object.keys(email.emailVars);

  console.log(
    `EMAILCRON > EMAILORGANIZE > PROCESS EMAIL WITH VARIABLES > ${recipients.length} recipients`
  );

  // Criar uma mensagem individual para cada destinatário
  for (const recipient of recipients) {
    const recipientVars = email.emailVars[recipient];

    // Criar objeto de email com dados específicos do destinatário
    const emailWithRecipient = {
      ...email,
      to: recipient,
      emailVars: { [recipient]: recipientVars },
      recipient_data: recipientVars,
      scheduled_date: new Date(scheduledTime).toISOString(),
      created_at: new Date().toISOString(),
      attempts: 0,
      status: "scheduled",
    };

    // Gerar ID único para o email
    const emailId = `${email.id || "email"}_${recipient.replace(/[^a-zA-Z0-9]/g, "_")}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

    // Salvar no Redis
    const saved = await saveEmailInRedis(
      emailId,
      emailWithRecipient,
      scheduledTime
    );

    if (saved) {
      console.log(
        `EMAILCRON > EMAILORGANIZE > PROCESS EMAIL WITH VARIABLES > Saved email for ${recipient}`
      );
    } else {
      console.error(
        `EMAILCRON > EMAILORGANIZE > PROCESS EMAIL WITH VARIABLES > Failed to save email for ${recipient}`
      );
    }
  }
};

/**
 * Processa email simples (sem variáveis por destinatário)
 * @param {Object} email - Dados do email
 * @param {number} scheduledTime - Timestamp de agendamento
 * @returns {Promise<void>}
 */
const processSimpleEmail = async (email, scheduledTime) => {
  console.log(
    "EMAILCRON > EMAILORGANIZE > PROCESS SIMPLE EMAIL",
    "to:",
    email.to
  );

  // Criar objeto de email
  const emailWithTime = {
    ...email,
    scheduled_date: new Date(scheduledTime).toISOString(),
    created_at: new Date().toISOString(),
    attempts: 0,
    status: "scheduled",
  };

  // Gerar ID único para o email
  const emailId = `${email.id || "email"}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

  // Salvar no Redis
  const saved = await saveEmailInRedis(emailId, emailWithTime, scheduledTime);

  if (saved) {
    console.log(
      "EMAILCRON > EMAILORGANIZE > PROCESS SIMPLE EMAIL > Email saved successfully"
    );
  } else {
    console.error(
      "EMAILCRON > EMAILORGANIZE > PROCESS SIMPLE EMAIL > Failed to save email"
    );
  }
};

/**
 * Salva um email no Redis com um timestamp para agendamento
 * @param {string} emailId - ID único do email
 * @param {Object} email - Objeto do email
 * @param {number} scheduledTime - Timestamp de agendamento
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const saveEmailInRedis = async (emailId, email, scheduledTime) => {
  try {
    // Validar parâmetros
    if (!emailId) {
      console.error(
        "EMAILCRON > EMAILORGANIZE > SAVE EMAIL > ERROR: Email ID is required"
      );
      return false;
    }

    if (!email) {
      console.error(
        "EMAILCRON > EMAILORGANIZE > SAVE EMAIL > ERROR: Email content is required"
      );
      return false;
    }

    if (!scheduledTime || isNaN(scheduledTime)) {
      console.error(
        "EMAILCRON > EMAILORGANIZE > SAVE EMAIL > ERROR: Valid scheduled time is required"
      );
      return false;
    }

    // Adicionar metadados ao email
    const emailWithMetadata = {
      ...email,
      redis_key: emailId,
      id: emailId,
      scheduled_timestamp: scheduledTime,
      created_at: email.created_at || new Date().toISOString(),
      status: email.status || "scheduled",
    };

    // Chave da lista ordenada de emails agendados
    const scheduledListKey = "email:scheduled_messages";

    console.log(
      "EMAILCRON > EMAILORGANIZE > SAVE EMAIL > BEFORE SAVE",
      "emailId:",
      emailId,
      "scheduledTime:",
      scheduledTime,
      "scheduledDate:",
      new Date(scheduledTime).toISOString(),
      "to:",
      email.to
    );

    // Salvar no Redis usando a função do redisClient
    const success = await saveScheduledMessage(
      scheduledListKey,
      scheduledTime,
      emailId,
      emailWithMetadata
    );

    if (success) {
      console.log(
        "EMAILCRON > EMAILORGANIZE > SAVE EMAIL > SUCCESS",
        "emailId:",
        emailId,
        "saved to Redis"
      );
    } else {
      console.error(
        "EMAILCRON > EMAILORGANIZE > SAVE EMAIL > FAILED",
        "emailId:",
        emailId,
        "failed to save to Redis"
      );
    }

    return success;
  } catch (error) {
    console.error(
      "EMAILCRON > EMAILORGANIZE > SAVE EMAIL > ERROR:",
      error.message
    );
    console.error(error.stack);
    return false;
  }
};

module.exports = {
  emailOrganizeMessages,
  prepareEmailAndSaveInRedis,
  processEmailWithVariables,
  processSimpleEmail,
  saveEmailInRedis,
};
