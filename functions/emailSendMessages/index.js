const { getScheduledMessages, removeMessage } = require("../utils/redisClient");
const { FirestoreRef, CONSTANTS } = require("../init");
const { momentNow } = require("../helpers");
const moment = require("moment");
const dotenv = require("dotenv");

// Função para importar sendMail de forma lazy (evitar dependência circular)
const getSendMailFunction = () => {
  try {
    const mailing = require("../mailing");
    if (mailing && typeof mailing.sendMail === "function") {
      return mailing.sendMail;
    } else {
      console.error(
        "EMAILCRON > EMAIL SEND MESSAGES > sendMail not found in mailing module"
      );
      return null;
    }
  } catch (error) {
    console.error(
      "EMAILCRON > EMAIL SEND MESSAGES > Error importing sendMail:",
      error.message
    );
    return null;
  }
};

// Carregar variáveis de ambiente do arquivo .env
dotenv.config();

/**
 * Função principal para processar e enviar emails do Redis
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Array>} - Lista de emails processados
 */
const emailSendMessages = async (options = {}) => {
  try {
    // Processar os emails agendados
    const processedEmails = await processScheduledEmails(options);

    console.log(
      `EMAILCRON > EMAIL SEND MESSAGES > Processados ${processedEmails.length} emails`
    );

    return processedEmails;
  } catch (error) {
    console.error(
      "EMAILCRON > EMAIL SEND MESSAGES > Erro no processamento:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Busca e imprime os emails agendados do Redis
 * @returns {Promise<Array>} Lista de emails
 */
const getScheduledEmailsFromRedis = async () => {
  try {
    const scheduledListKey = "email:scheduled_messages";
    let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
    let momentNowInstanceTimestamp = new Date(momentNowISO).getTime();

    console.log(
      "EMAILCRON > EMAIL SEND MESSAGES > GET SCHEDULED EMAILS",
      "currentTime:",
      momentNowISO,
      "timestamp:",
      momentNowInstanceTimestamp
    );

    // Obter emails agendados até o momento atual
    const emails = await getScheduledMessages(
      scheduledListKey,
      momentNowInstanceTimestamp,
      "GET",
      {
        limit: 50, // Processar até 50 emails por vez
        remove: false,
      }
    );

    if (emails.length > 0) {
      console.log(
        `EMAILCRON > EMAIL SEND MESSAGES > Found ${emails.length} scheduled emails`
      );

      emails.forEach((email, index) => {
        const scheduledDate = email.scheduled_timestamp
          ? new Date(email.scheduled_timestamp).toISOString()
          : "unknown";

        console.log(
          `EMAILCRON > EMAIL SEND MESSAGES > Email ${index + 1}:`,
          "id:",
          email.id,
          "to:",
          email.to,
          "subject:",
          email.subject,
          "scheduledDate:",
          scheduledDate
        );
      });
    } else {
      console.log(
        "EMAILCRON > EMAIL SEND MESSAGES > No scheduled emails found"
      );
    }

    return emails;
  } catch (error) {
    console.error(
      "EMAILCRON > EMAIL SEND MESSAGES > Error getting scheduled emails:",
      error.message
    );
    return [];
  }
};

/**
 * Processa emails agendados do Redis
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Array>} - Lista de emails processados
 */
const processScheduledEmails = async (options = {}) => {
  const {
    batchSize = 10, // Processar até 10 emails por vez
    dryRun = false, // Modo de simulação
  } = options;

  console.log(
    "EMAILCRON > EMAIL SEND MESSAGES > PROCESS SCHEDULED EMAILS",
    "batchSize:",
    batchSize,
    "dryRun:",
    dryRun
  );

  try {
    // Chave da lista ordenada de emails agendados
    const scheduledListKey = "email:scheduled_messages";

    let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
    let momentNowInstanceTimestamp = new Date(momentNowISO).getTime();

    // Obter os emails agendados no Redis
    const emails = await getScheduledMessages(
      scheduledListKey,
      momentNowInstanceTimestamp,
      "PROCESS",
      {
        limit: batchSize,
        remove: false,
      }
    );

    if (emails.length === 0) {
      console.log("EMAILCRON > EMAIL SEND MESSAGES > NO EMAILS");
      return [];
    }

    const totalEmails = emails.length;
    console.log(
      `EMAILCRON > EMAIL SEND MESSAGES > TOTAL EMAILS ${totalEmails} TO PROCESS`
    );

    let emailIndex = 0;
    // Processar emails em paralelo para melhor performance
    let emailsProcessed = [];
    const emailsProcess = emails.map(async (email) => {
      let results = {};
      try {
        const emailId =
          email.id ||
          `email_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

        // Registrar o email no Firestore antes de enviar
        await FirestoreRef.collection("emails_sent")
          .doc(emailId)
          .set({
            ...email,
            _processing_started_at: momentNow().format(CONSTANTS.MOMENT_ISO),
            status: "processing",
            _dry_run: dryRun,
          });

        // Se for modo de simulação, não enviar realmente
        if (dryRun) {
          console.log(
            `EMAILCRON > EMAIL SEND MESSAGES > [DRY RUN] Would send email ${emailId}`
          );

          // Atualizar status no Firestore
          await FirestoreRef.collection("emails_sent")
            .doc(emailId)
            .update({
              status: "simulated",
              _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
            });

          // Usar a redis_key do email para remoção correta
          if (email.redis_key) {
            await removeMessage(email.redis_key, "email:scheduled_messages");
          }
          return { ...email, id: emailId, status: "simulated" };
        }

        emailsProcessed.push(email);

        emailIndex += 1;
        console.log(
          `EMAILCRON > EMAIL SEND MESSAGES > BEFORE > PROCESSING EMAIL ${emailIndex} OF ${totalEmails}`
        );

        // Enviar o email usando a função sendMail existente
        results = await sendEmailToProvider(email);

        if (results && results.success) {
          console.log(`EMAILCRON > EMAIL SEND MESSAGES > ENVIO BEM-SUCEDIDO`);

          // Atualizar status no Firestore
          await FirestoreRef.collection("emails_sent")
            .doc(emailId)
            .update({
              status: "sent",
              _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
              _send_result: results,
            });

          // Marcar documento original como processado/enviado
          await markOriginalDocumentAsProcessed(email);

          // Remover o email do Redis usando a redis_key
          if (email.redis_key) {
            const success = await removeMessage(
              email.redis_key,
              "email:scheduled_messages"
            );
            if (success) {
              console.log(
                `EMAILCRON > EMAIL SEND MESSAGES > EMAIL ${email.redis_key} REMOVIDO DO REDIS`
              );
            } else {
              console.error(
                `EMAILCRON > EMAIL SEND MESSAGES > ERRO AO REMOVER EMAIL ${email.redis_key} DO REDIS`
              );
            }
          }
        } else {
          console.warn(
            `EMAILCRON > EMAIL SEND MESSAGES > FALHA NO ENVIO - EMAIL MANTIDO NO REDIS`
          );

          // Atualizar status no Firestore
          await FirestoreRef.collection("emails_sent")
            .doc(emailId)
            .update({
              status: "failed",
              _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
              _send_result: results,
              _error: results.error || "Unknown error",
            });
        }

        return { ...email, id: emailId, results };
      } catch (error) {
        console.error(
          `EMAILCRON > EMAIL SEND MESSAGES > ERRO AO PROCESSAR EMAIL:`,
          error.message
        );

        // Atualizar status no Firestore em caso de erro
        const emailId = email.id || `email_error_${Date.now()}`;
        await FirestoreRef.collection("emails_sent")
          .doc(emailId)
          .set({
            ...email,
            status: "error",
            _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
            _error: error.message,
            _error_stack: error.stack,
          });

        return { ...email, id: emailId, error: error.message };
      }
    });

    // Aguardar o processamento de todos os emails
    const processedResults = await Promise.all(emailsProcess);

    console.log(
      `EMAILCRON > EMAIL SEND MESSAGES > PROCESSAMENTO CONCLUÍDO: ${processedResults.length} emails processados`
    );

    return processedResults;
  } catch (error) {
    console.error(
      "EMAILCRON > EMAIL SEND MESSAGES > ERRO NO PROCESSAMENTO:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Envia um email usando os providers disponíveis
 * @param {Object} email - Dados do email
 * @returns {Promise<Object>} - Resultado do envio
 */
const sendEmailToProvider = async (email) => {
  try {
    console.log(
      "EMAILCRON > EMAIL SEND MESSAGES > SEND EMAIL TO PROVIDER",
      "emailId:",
      email.id,
      "to:",
      email.to,
      "subject:",
      email.subject
    );

    // Obter a função sendMail de forma lazy
    const sendMail = getSendMailFunction();
    if (!sendMail) {
      console.error(
        "EMAILCRON > EMAIL SEND MESSAGES > sendMail function not available"
      );
      return { success: false, error: "sendMail function not available" };
    }

    console.log(
      "EMAILCRON > EMAIL SEND MESSAGES > sendMail function loaded successfully"
    );

    // Criar uma referência temporária no Firestore para o sendMail
    const emailRef = FirestoreRef.collection("emails_temp").doc(email.id);

    // Preparar dados no formato esperado pelo sendMail
    const emailData = {
      ...email,
      // Garantir que os campos obrigatórios estejam presentes
      to: email.to,
      from: email.from || email.fromEmail,
      fromName: email.fromName,
      subject: email.subject,
      html: email.html,
      cc: email.cc || "",
      bcc: email.bcc || "",
      scheduled_date: email.scheduled_date,
      context: email.context || {},
      emailVars: email.emailVars,
      owner: email.owner,
      accountId: email.accountId,
      smtp: email.smtp,
      integrationId: email.integrationId,
    };

    console.log(
      "EMAILCRON > EMAIL SEND MESSAGES > CALLING sendMail with data:",
      {
        to: emailData.to,
        subject: emailData.subject,
        smtp: emailData.smtp,
        hasHtml: !!emailData.html,
        hasEmailVars: !!emailData.emailVars,
      }
    );

    // Usar a função sendMail existente do sistema de mailing
    const result = await sendMail(emailData, emailRef);

    // Limpar a referência temporária
    try {
      await emailRef.delete();
    } catch (deleteError) {
      console.warn(
        "EMAILCRON > EMAIL SEND MESSAGES > Warning: Could not delete temp email ref:",
        deleteError.message
      );
    }

    if (result) {
      console.log("EMAILCRON > EMAIL SEND MESSAGES > sendMail result:", result);
      return { success: true, result };
    } else {
      console.warn(
        "EMAILCRON > EMAIL SEND MESSAGES > sendMail returned null/undefined"
      );
      return { success: false, error: "sendMail returned null" };
    }
  } catch (error) {
    console.error(
      "EMAILCRON > EMAIL SEND MESSAGES > Error in sendEmailToProvider:",
      error.message
    );
    console.error(error.stack);
    return { success: false, error: error.message };
  }
};

/**
 * Marca o documento original no Firestore como processado após envio bem-sucedido
 * @param {Object} email - Dados do email processado
 * @returns {Promise<void>}
 */
const markOriginalDocumentAsProcessed = async (email) => {
  try {
    const now = momentNow().format(CONSTANTS.MOMENT_ISO);

    // Marcar cronjob como executado se veio de um cronjob
    if (email.cronjob_id && email.cronjob_ref) {
      console.log(
        `EMAILCRON > EMAIL SEND MESSAGES > Marking cronjob ${email.cronjob_id} as executed`
      );

      await email.cronjob_ref.update({
        executed: true,
        execution_date: now,
        processed_by_redis: true,
      });
    }

    // Marcar email direto como enviado se veio da collection de emails
    if (email.firestore_ref && !email.cronjob_id) {
      console.log(
        `EMAILCRON > EMAIL SEND MESSAGES > Marking email ${email.id} as sent`
      );

      await email.firestore_ref.update({
        sent: true,
        sending: false,
        error: false,
        sentDate: now,
        processed_by_redis: true,
      });
    }

    console.log(
      `EMAILCRON > EMAIL SEND MESSAGES > Original document marked as processed for email ${email.id}`
    );
  } catch (error) {
    console.error(
      `EMAILCRON > EMAIL SEND MESSAGES > Error marking original document as processed:`,
      error.message
    );
    // Não falhar o processo por causa deste erro - apenas logar
  }
};

module.exports = {
  emailSendMessages,
  getScheduledEmailsFromRedis,
  processScheduledEmails,
  sendEmailToProvider,
  markOriginalDocumentAsProcessed,
};
